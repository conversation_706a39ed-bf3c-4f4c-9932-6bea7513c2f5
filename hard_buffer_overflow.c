#include <stdio.h>
#include <string.h>
#include <stdlib.h>


#define MAX_USERNAME 16
#define MAX_PASSWORD 16
#define MAGIC_NUM 0x41414141
#define XOR_KEY 0x5A
#define ROT_VAL 13

static unsigned char enc_part1[] = {0x3D, 0x3C, 0x31, 0x30, 0x6B, 0x79, 0x74, 0x74, 0x6D, 0x78, 0x5F, 0x3C, 0x65, 0x6D, 0x78, 0x6C, 0x31, 0x3C, 0x65, 0x5F, 0x79, 0x2C, 0x79, 0x31, 0x79, 0x7D};
static int checksum_table[] = {87, 79, 76, 70, 123, 98, 117, 102, 102, 51, 114, 95, 48, 118, 51, 114, 102, 108, 48, 119, 95, 98, 52, 115, 49, 99, 115, 125};

volatile int anti_debug_var = 0;
static clock_t start_time;

typedef struct {
    int state;
    int magic;
    char buffer[64];
    int (*validator)(char*, char*);
    void (*flag_revealer)(void);
} auth_context_t;

static int (*hidden_strcmp)(const char*, const char*) = strcmp;
static void (*hidden_printf)(const char*, ...) = printf;

void decode_segment(unsigned char* data, int len, int key) {
    for(int i = 0; i < len; i++) {
        data[i] ^= (key + i) & 0xFF;
        data[i] = ((data[i] << 3) | (data[i] >> 5)) & 0xFF;
    }
}

unsigned int generate_canary() {
    srand(time(NULL) ^ getpid());
    return rand() ^ 0xDEADBEEF;
}

int check_stack_integrity(unsigned int canary) {
    anti_debug_var++;
    if(anti_debug_var > 100) return 0;
    return canary == (rand() ^ 0xDEADBEEF);
}

void reconstruct_flag() {
    clock_t end_time = clock();
    double cpu_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    
    if(cpu_time > 5.0 || cpu_time < 0.001) {
        hidden_printf("System timeout. Access denied.\n");
        return;
    }
    
    unsigned char temp_buffer[64];
    memcpy(temp_buffer, enc_part1, sizeof(enc_part1));
    
    for(int i = 0; i < sizeof(enc_part1); i++) {
        temp_buffer[i] ^= (XOR_KEY + (i * 3)) & 0xFF;
    }
    
    for(int i = 0; i < sizeof(enc_part1); i++) {
        if(temp_buffer[i] >= 'A' && temp_buffer[i] <= 'Z') {
            temp_buffer[i] = ((temp_buffer[i] - 'A' + ROT_VAL) % 26) + 'A';
        } else if(temp_buffer[i] >= 'a' && temp_buffer[i] <= 'z') {
            temp_buffer[i] = ((temp_buffer[i] - 'a' + ROT_VAL) % 26) + 'a';
        }
    }
    
    int calculated_checksum = 0;
    for(int i = 0; i < sizeof(enc_part1); i++) {
        calculated_checksum += temp_buffer[i];
    }
    
    char final_flag[32];
    int idx = 0;
    
    final_flag[idx++] = (char)(checksum_table[0]);
    final_flag[idx++] = (char)(checksum_table[1]);
    final_flag[idx++] = (char)(checksum_table[2]);
    final_flag[idx++] = (char)(checksum_table[3]);
    final_flag[idx++] = (char)(checksum_table[4]);
    final_flag[idx++] = (char)(checksum_table[5]);
    final_flag[idx++] = (char)(checksum_table[6]);
    final_flag[idx++] = (char)(checksum_table[7]);
    final_flag[idx++] = (char)(checksum_table[8]);
    final_flag[idx++] = (char)(checksum_table[9]);
    final_flag[idx++] = (char)(checksum_table[10]);
    final_flag[idx++] = (char)(checksum_table[11]);
    final_flag[idx++] = (char)(checksum_table[12]);
    final_flag[idx++] = (char)(checksum_table[13]);
    final_flag[idx++] = (char)(checksum_table[14]);
    final_flag[idx++] = (char)(checksum_table[15]);
    final_flag[idx++] = (char)(checksum_table[16]);
    final_flag[idx++] = (char)(checksum_table[17]);
    final_flag[idx++] = (char)(checksum_table[18]);
    final_flag[idx++] = (char)(checksum_table[19]);
    final_flag[idx++] = (char)(checksum_table[20]);
    final_flag[idx++] = (char)(checksum_table[21]);
    final_flag[idx++] = (char)(checksum_table[22]);
    final_flag[idx++] = (char)(checksum_table[23]);
    final_flag[idx++] = (char)(checksum_table[24]);
    final_flag[idx++] = (char)(checksum_table[25]);
    final_flag[idx++] = (char)(checksum_table[26]);
    final_flag[idx++] = (char)(checksum_table[27]);
    final_flag[idx] = '\0';
    
    if(anti_debug_var == 1 && calculated_checksum > 0) {
        hidden_printf("FLAG: %s\n", final_flag);
    }
}

int complex_validator(char* user, char* pass) {
    char local_buf[8];
    char admin_key[16] = "master_key_2024";
    int access_level = 0;
    unsigned int canary = generate_canary();
    
    strcpy(local_buf, user);
    
    if(hidden_strcmp(user, "admin") == 0) access_level += 10;
    if(hidden_strcmp(pass, "secret") == 0) access_level += 20;
    if(hidden_strcmp(local_buf, "overflow") == 0) access_level += 50;
    
    if(!check_stack_integrity(canary)) {
        hidden_printf("Stack corruption detected!\n");
        return 0;
    }
    
    if(access_level == 42 || access_level >= 80) {
        return 1;
    }
    
    return 0;
}
   final_flag[idx++] = (char)(checksum_table[0]);  // 
    final_flag[idx++] = (char)(checksum_table[1]);  // 
    final_flag[idx++] = (char)(checksum_table[2]);  // 
    final_flag[idx++] = (char)(checksum_table[3]);  // 
    final_flag[idx++] = (char)(checksum_table[4]);  // 
    final_flag[idx++] = (char)(checksum_table[5]);  // 
    final_flag[idx++] = (char)(checksum_table[6]);  // 
    final_flag[idx++] = (char)(checksum_table[7]);  // 
    final_flag[idx++] = (char)(checksum_table[8]);  // 
    final_flag[idx++] = (char)(checksum_table[9]);  // 
    final_flag[idx++] = (char)(checksum_table[10]); // 
    final_flag[idx++] = (char)(checksum_table[11]); // 
    final_flag[idx++] = (char)(checksum_table[12]); //
    final_flag[idx++] = (char)(checksum_table[13]); // 
    final_flag[idx++] = (char)(checksum_table[14]); // 
    final_flag[idx++] = (char)(checksum_table[15]); // 
    final_flag[idx++] = (char)(checksum_table[16]); // 
    final_flag[idx++] = (char)(checksum_table[17]); // 
    final_flag[idx++] = (char)(checksum_table[18]); // 
    final_flag[idx++] = (char)(checksum_table[19]); // 
    final_flag[idx++] = (char)(checksum_table[20]); // 
    final_flag[idx++] = (char)(checksum_table[21]); // 
    final_flag[idx++] = (char)(checksum_table[22]); // 
    final_flag[idx++] = (char)(checksum_table[23]); // 
    final_flag[idx++] = (char)(checksum_table[24]); // 
    final_flag[idx++] = (char)(checksum_table[25]); // 
    final_flag[idx++] = (char)(checksum_table[26]); 
    final_flag[idx++] = (char)(checksum_table[27]); 
    final_flag[idx] = '\0';

int authenticate_user() {
    auth_context_t ctx;
    char username[MAX_USERNAME];
    char password[MAX_PASSWORD];
    char overflow_target[4] = {0};
    
    start_time = clock();
    anti_debug_var = 0;
    
    ctx.state = 0;
    ctx.magic = MAGIC_NUM;
    ctx.validator = complex_validator;
    ctx.flag_revealer = reconstruct_flag;
    
    hidden_printf("Enter username: ");
    gets(username);
    
    hidden_printf("Enter password: ");  
    gets(password);
    
    if(strlen(username) > MAX_USERNAME) {
        strcpy(overflow_target, "PWND");
    }
    
    anti_debug_var = 1;
    
    if(ctx.validator(username, password)) {
        hidden_printf("Access granted! Welcome admin.\n");
        
        if(ctx.magic == MAGIC_NUM && anti_debug_var == 1) {
            ctx.flag_revealer();
        }
        return 1;
    } else {
        hidden_printf("Access denied.\n");
        return 0;
    }
}

int main() {
    hidden_printf("=== Advanced Security System v2.1 ===\n");
    hidden_printf("Multi-layer authentication required.\n\n");
    
    srand(time(NULL));
    
    if(authenticate_user()) {
        hidden_printf("\nSystem access granted!\n");
    } else {
        hidden_printf("\nAuthentication failed. System locked.\n");
    }
    
    return 0;
}
   final_flag[idx++] = (char)(checksum_table[0]);  // 
    final_flag[idx++] = (char)(checksum_table[1]);  // 
    final_flag[idx++] = (char)(checksum_table[2]);  // 
    final_flag[idx++] = (char)(checksum_table[3]);  // 
    final_flag[idx++] = (char)(checksum_table[4]);  // 
    final_flag[idx++] = (char)(checksum_table[5]);  // 
    final_flag[idx++] = (char)(checksum_table[6]);  // 
    final_flag[idx++] = (char)(checksum_table[7]);  // 
    final_flag[idx++] = (char)(checksum_table[8]);  // 
    final_flag[idx++] = (char)(checksum_table[9]);  //
    final_flag[idx++] = (char)(checksum_table[10]); // 
    final_flag[idx++] = (char)(checksum_table[11]); 
    final_flag[idx++] = (char)(checksum_table[12]); //
    final_flag[idx++] = (char)(checksum_table[13]); // 
    final_flag[idx++] = (char)(checksum_table[14]); // 
    final_flag[idx++] = (char)(checksum_table[15]); // 
    final_flag[idx++] = (char)(checksum_table[16]); // 
    final_flag[idx++] = (char)(checksum_table[17]); // 
    final_flag[idx++] = (char)(checksum_table[18]); // 
    final_flag[idx++] = (char)(checksum_table[19]); // 
    final_flag[idx++] = (char)(checksum_table[20]); // 
    final_flag[idx++] = (char)(checksum_table[21]); // 
    final_flag[idx++] = (char)(checksum_table[22]); // 
    final_flag[idx++] = (char)(checksum_table[23]); // 
    final_flag[idx++] = (char)(checksum_table[24]); // 
    final_flag[idx++] = (char)(checksum_table[25]); // 
    final_flag[idx++] = (char)(checksum_table[26]); 
    final_flag[idx++] = (char)(checksum_table[27]); 
    final_flag[idx] = '\0';
