<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTF - Buffer Overflow Challenge</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #0d1117;
            color: #c9d1d9;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #161b22;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #30363d;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            color: #f85149;
            font-size: 2.5em;
            margin: 0;
            text-shadow: 0 0 10px rgba(248, 81, 73, 0.3);
        }
        .difficulty {
            background-color: #ffa500;
            color: #000;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .points {
            color: #7c3aed;
            font-size: 1.2em;
            font-weight: bold;
        }
        .description {
            background-color: #21262d;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #58a6ff;
            margin: 20px 0;
        }
        .code-section {
            background-color: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .code-title {
            color: #58a6ff;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        pre {
            margin: 0;
            color: #c9d1d9;
            font-size: 14px;
            line-height: 1.4;
        }
        .hint-section {
            background-color: #1f2937;
            border: 1px solid #374151;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .hint-toggle {
            background-color: #6366f1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        .hint-toggle:hover {
            background-color: #4f46e5;
        }
        .hint-content {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background-color: #111827;
            border-radius: 4px;
            border-left: 3px solid #fbbf24;
        }
        .flag-section {
            background-color: #1e293b;
            border: 1px solid #334155;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .flag-input {
            width: 70%;
            padding: 10px;
            background-color: #0f172a;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
        }
        .submit-btn {
            background-color: #10b981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
            font-family: inherit;
        }
        .submit-btn:hover {
            background-color: #059669;
        }
        .vulnerability {
            color: #f85149;
            font-weight: bold;
        }
        .safe-function {
            color: #7c3aed;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">BUFFER OVERFLOW</h1>
            <div class="points">Points: 50</div>
            <div class="difficulty">Medium</div>
        </div>

        <div class="description">
            <h3>Challenge Description</h3>
            <p>Find the buffer overflow vulnerability in this C code snippet. The program is designed to authenticate users, but contains a critical security flaw that could allow an attacker to bypass authentication.</p>
            <p><strong>Your task:</strong> Identify the vulnerable function and explain how an attacker could exploit it.</p>
        </div>

        <div class="code-section">
            <div class="code-title">📁 vulnerable_auth.c</div>
            <pre><code>#include &lt;stdio.h&gt;
#include &lt;string.h&gt;
#include &lt;stdlib.h&gt;

#define MAX_USERNAME 16
#define MAX_PASSWORD 16

int authenticate_user() {
    char username[MAX_USERNAME];
    char password[MAX_PASSWORD];
    int is_admin = 0;
    
    printf("Enter username: ");
    <span class="vulnerability">gets(username);</span>  // Vulnerable function!
    
    printf("Enter password: ");
    <span class="vulnerability">gets(password);</span>  // Another vulnerable function!
    
    // Simple authentication check
    if (strcmp(username, "admin") == 0 && strcmp(password, "secret123") == 0) {
        is_admin = 1;
    }
    
    if (is_admin) {
        printf("Access granted! Welcome admin.\n");
        printf("FLAG: CTF{buff3r_0v3rfl0w_1s_d4ng3r0us}\n");
        return 1;
    } else {
        printf("Access denied.\n");
        return 0;
    }
}

int main() {
    printf("=== Secure Authentication System ===\n");
    printf("Please provide your credentials:\n\n");
    
    if (authenticate_user()) {
        printf("\nYou have successfully logged in!\n");
    } else {
        printf("\nAuthentication failed. Goodbye!\n");
    }
    
    return 0;
}</code></pre>
        </div>

        <div class="hint-section">
            <button class="hint-toggle" onclick="toggleHint()">💡 Show Hint</button>
            <div class="hint-content" id="hintContent">
                <strong>Hint:</strong> Look for unsafe string functions that don't check buffer boundaries. 
                The <code>gets()</code> function is notorious for buffer overflow vulnerabilities because it doesn't limit input size.
                <br><br>
                <strong>Additional clue:</strong> Notice how the variables are arranged in memory. What happens if you input more than 16 characters?
            </div>
        </div>

        <div class="code-section">
            <div class="code-title">🔧 How to fix this vulnerability:</div>
            <pre><code>// Replace dangerous gets() with safer alternatives:

// Option 1: Use fgets() with size limit
<span class="safe-function">fgets(username, MAX_USERNAME, stdin);</span>

// Option 2: Use scanf() with width specifier  
<span class="safe-function">scanf("%15s", username);</span>  // 15 chars + null terminator

// Option 3: Use more modern secure functions (if available)
<span class="safe-function">gets_s(username, MAX_USERNAME);</span>  // C11 standard</code></pre>
        </div>

        <div class="flag-section">
            <h3>🚩 Submit Flag</h3>
            <p>Enter the flag you found in the code:</p>
            <input type="text" class="flag-input" id="flagInput" placeholder="CTF{...}">
            <button class="submit-btn" onclick="checkFlag()">Submit</button>
            <div id="result" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        function toggleHint() {
            const hintContent = document.getElementById('hintContent');
            const button = document.querySelector('.hint-toggle');
            
            if (hintContent.style.display === 'none' || hintContent.style.display === '') {
                hintContent.style.display = 'block';
                button.textContent = '🔒 Hide Hint';
            } else {
                hintContent.style.display = 'none';
                button.textContent = '💡 Show Hint';
            }
        }

        function checkFlag() {
            const input = document.getElementById('flagInput').value.trim();
            const correctFlag = 'CTF{buff3r_0v3rfl0w_1s_d4ng3r0us}';
            const result = document.getElementById('result');
            
            if (input === correctFlag) {
                result.innerHTML = '<div style="color: #10b981; font-weight: bold;">🎉 Correct! Well done!</div>';
            } else {
                result.innerHTML = '<div style="color: #f85149; font-weight: bold;">❌ Incorrect flag. Try again!</div>';
            }
        }

        // Hidden enhancement: Konami code easter egg
        let konamiCode = [];
        const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // ↑↑↓↓←→←→BA
        
        document.addEventListener('keydown', function(e) {
            konamiCode.push(e.keyCode);
            if (konamiCode.length > konamiSequence.length) {
                konamiCode.shift();
            }
            
            if (konamiCode.length === konamiSequence.length && 
                konamiCode.every((code, index) => code === konamiSequence[index])) {
                
                // Easter egg activated
                document.body.style.animation = 'rainbow 2s infinite';
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes rainbow {
                        0% { filter: hue-rotate(0deg); }
                        100% { filter: hue-rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);
                
                setTimeout(() => {
                    alert('🎮 Konami Code activated! You found the hidden enhancement!');
                }, 1000);
            }
        });
    </script>
</body>
</html>
