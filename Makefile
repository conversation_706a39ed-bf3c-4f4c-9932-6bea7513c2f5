# Makefile for Buffer Overflow CTF Challenge

CC = gcc
CFLAGS = -Wall -Wextra -std=c99
VULNERABLE_FLAGS = -fno-stack-protector -z execstack -no-pie
SECURE_FLAGS = -fstack-protector-strong -D_FORTIFY_SOURCE=2

# Targets
all: vulnerable secure

vulnerable: vulnerable_auth.c
	$(CC) $(CFLAGS) $(VULNERABLE_FLAGS) -o vulnerable_auth vulnerable_auth.c
	@echo "Compiled vulnerable version (stack protection disabled)"

secure: secure_auth.c
	$(CC) $(CFLAGS) $(SECURE_FLAGS) -o secure_auth secure_auth.c
	@echo "Compiled secure version (with stack protection)"

# Test the vulnerable version
test-vulnerable: vulnerable
	@echo "Testing vulnerable version..."
	@echo "Try entering more than 16 characters for username to trigger overflow"
	./vulnerable_auth

# Test the secure version
test-secure: secure
	@echo "Testing secure version..."
	@echo "This version is protected against buffer overflow"
	./secure_auth

# Demonstrate the exploit
exploit: vulnerable
	@echo "Demonstrating buffer overflow exploit..."
	@echo "Username: AAAAAAAAAAAAAAAA1 (16 A's + 1)"
	@echo "Password: anything"
	@echo "AAAAAAAAAAAAAAAA1\nanything" | ./vulnerable_auth

# Clean up compiled files
clean:
	rm -f vulnerable_auth secure_auth

# Show vulnerability explanation
explain:
	@echo "=== BUFFER OVERFLOW VULNERABILITY EXPLANATION ==="
	@echo ""
	@echo "The vulnerable code uses gets() function which:"
	@echo "1. Does not check buffer boundaries"
	@echo "2. Allows input longer than allocated buffer size"
	@echo "3. Can overwrite adjacent memory locations"
	@echo ""
	@echo "Stack layout (approximate):"
	@echo "[username buffer][password buffer][is_admin][return address]"
	@echo ""
	@echo "Exploit: Input 17+ characters for username to overwrite is_admin"
	@echo "Example: 'AAAAAAAAAAAAAAAA1' (16 A's + '1')"
	@echo ""
	@echo "The '1' character (ASCII 49) overwrites is_admin, making it non-zero"
	@echo "This bypasses authentication and grants admin access!"

.PHONY: all vulnerable secure test-vulnerable test-secure exploit clean explain
