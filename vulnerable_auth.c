#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>

#define MAX_USERNAME 16
#define MAX_PASSWORD 16
#define MAGIC_NUM 0x41414141
#define XOR_KEY 0x5A
#define ROT_VAL 13

// Obfuscated flag components - split and encoded
static unsigned char enc_part1[] = {0x3D, 0x3C, 0x31, 0x30, 0x6B, 0x79, 0x74, 0x74, 0x6D, 0x78, 0x5F, 0x3C, 0x65, 0x6D, 0x78, 0x6C, 0x31, 0x3C, 0x65, 0x5F, 0x79, 0x2C, 0x79, 0x31, 0x79, 0x7D};
static int checksum_table[] = {87, 79, 76, 70, 123, 98, 117, 102, 102, 51, 114, 95, 48, 118, 51, 114, 102, 108, 48, 119, 95, 98, 52, 115, 49, 99, 115, 125};

// Anti-debugging and timing checks
volatile int anti_debug_var = 0;
static clock_t start_time;

// Complex authentication state machine
typedef struct {
    int state;
    int magic;
    char buffer[64];
    int (*validator)(char*, char*);
    void (*flag_revealer)(void);
} auth_context_t;

// Function pointer obfuscation
static int (*hidden_strcmp)(const char*, const char*) = strcmp;
static void (*hidden_printf)(const char*, ...) = printf;

// Polymorphic XOR decoder
void decode_segment(unsigned char* data, int len, int key) {
    for(int i = 0; i < len; i++) {
        data[i] ^= (key + i) & 0xFF;
        data[i] = ((data[i] << 3) | (data[i] >> 5)) & 0xFF;
    }
}

// Stack canary simulation with fake protection
unsigned int generate_canary() {
    srand(time(NULL) ^ getpid());
    return rand() ^ 0xDEADBEEF;
}

// Fake vulnerability checker
int check_stack_integrity(unsigned int canary) {
    anti_debug_var++;
    if(anti_debug_var > 100) return 0;
    return canary == (rand() ^ 0xDEADBEEF);
}

// Complex flag reconstruction algorithm
void reconstruct_flag() {
    clock_t end_time = clock();
    double cpu_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;

    // Anti-analysis: only work if execution time is reasonable
    if(cpu_time > 5.0 || cpu_time < 0.001) {
        hidden_printf("System timeout. Access denied.\n");
        return;
    }

    // Multi-stage decryption
    unsigned char temp_buffer[64];
    memcpy(temp_buffer, enc_part1, sizeof(enc_part1));

    // Stage 1: XOR with rotating key
    for(int i = 0; i < sizeof(enc_part1); i++) {
        temp_buffer[i] ^= (XOR_KEY + (i * 3)) & 0xFF;
    }

    // Stage 2: ROT cipher
    for(int i = 0; i < sizeof(enc_part1); i++) {
        if(temp_buffer[i] >= 'A' && temp_buffer[i] <= 'Z') {
            temp_buffer[i] = ((temp_buffer[i] - 'A' + ROT_VAL) % 26) + 'A';
        } else if(temp_buffer[i] >= 'a' && temp_buffer[i] <= 'z') {
            temp_buffer[i] = ((temp_buffer[i] - 'a' + ROT_VAL) % 26) + 'a';
        }
    }

    // Stage 3: Checksum validation with polymorphic verification
    int calculated_checksum = 0;
    for(int i = 0; i < sizeof(enc_part1); i++) {
        calculated_checksum += temp_buffer[i];
    }

    // Final stage: Reconstruct from checksum table (real flag hidden here)
    char final_flag[32];
    int idx = 0;

    // The real flag is embedded in the checksum validation logic
    final_flag[idx++] = (char)(checksum_table[0]);  // 
    final_flag[idx++] = (char)(checksum_table[1]);  // 
    final_flag[idx++] = (char)(checksum_table[2]);  // 
    final_flag[idx++] = (char)(checksum_table[3]);  // 
    final_flag[idx++] = (char)(checksum_table[4]);  // 
    final_flag[idx++] = (char)(checksum_table[5]);  // 
    final_flag[idx++] = (char)(checksum_table[6]);  // 
    final_flag[idx++] = (char)(checksum_table[7]);  // 
    final_flag[idx++] = (char)(checksum_table[8]);  // 
    final_flag[idx++] = (char)(checksum_table[9]);  // 
    final_flag[idx++] = (char)(checksum_table[10]); // 
    final_flag[idx++] = (char)(checksum_table[11]); // 
    final_flag[idx++] = (char)(checksum_table[12]); //
    final_flag[idx++] = (char)(checksum_table[13]); // 
    final_flag[idx++] = (char)(checksum_table[14]); // 
    final_flag[idx++] = (char)(checksum_table[15]); // 
    final_flag[idx++] = (char)(checksum_table[16]); // 
    final_flag[idx++] = (char)(checksum_table[17]); // 
    final_flag[idx++] = (char)(checksum_table[18]); // 
    final_flag[idx++] = (char)(checksum_table[19]); // 
    final_flag[idx++] = (char)(checksum_table[20]); // 
    final_flag[idx++] = (char)(checksum_table[21]); // 
    final_flag[idx++] = (char)(checksum_table[22]); // 
    final_flag[idx++] = (char)(checksum_table[23]); // 
    final_flag[idx++] = (char)(checksum_table[24]); // 
    final_flag[idx++] = (char)(checksum_table[25]); // 
    final_flag[idx++] = (char)(checksum_table[26]); 
    final_flag[idx++] = (char)(checksum_table[27]); 
    final_flag[idx] = '\0';

    // Anti-static analysis: only print if all conditions met
    if(anti_debug_var == 1 && calculated_checksum > 0) {
        hidden_printf("FLAG: %s\n", final_flag);
    }
}

// Advanced buffer overflow with multiple exploitation vectors
int complex_validator(char* user, char* pass) {
    // Intentionally vulnerable with multiple attack vectors
    char local_buf[8];  // Very small buffer for easy overflow
    char admin_key[16] = "master_key_2024";
    int access_level = 0;
    unsigned int canary = generate_canary();

    // Vulnerable strcpy - can overflow local_buf
    strcpy(local_buf, user);

    // Multiple comparison paths for confusion
    if(hidden_strcmp(user, "admin") == 0) access_level += 10;
    if(hidden_strcmp(pass, "secret") == 0) access_level += 20;
    if(hidden_strcmp(local_buf, "overflow") == 0) access_level += 50;

    // Stack canary check (fake security)
    if(!check_stack_integrity(canary)) {
        hidden_printf("Stack corruption detected!\n");
        return 0;
    }

    // Hidden backdoor: if access_level is exactly 42, grant access
    if(access_level == 42 || access_level >= 80) {
        return 1;
    }

    return 0;
}

int authenticate_user() {
    auth_context_t ctx;
    char username[MAX_USERNAME];
    char password[MAX_PASSWORD];
    char overflow_target[4] = {0};  // Easy overflow target

    start_time = clock();
    anti_debug_var = 0;

    ctx.state = 0;
    ctx.magic = MAGIC_NUM;
    ctx.validator = complex_validator;
    ctx.flag_revealer = reconstruct_flag;

    hidden_printf("Enter username: ");
    gets(username);  // Primary vulnerability

    hidden_printf("Enter password: ");
    gets(password);  // Secondary vulnerability

    // Additional vulnerability: buffer can overflow into overflow_target
    if(strlen(username) > MAX_USERNAME) {
        strcpy(overflow_target, "PWND");  // Indicator of successful overflow
    }

    anti_debug_var = 1;  // Set correct value for flag reconstruction

    // Complex authentication logic
    if(ctx.validator(username, password)) {
        hidden_printf("Access granted! Welcome admin.\n");

        // Only reveal flag if specific conditions are met
        if(ctx.magic == MAGIC_NUM && anti_debug_var == 1) {
            ctx.flag_revealer();
        }
        return 1;
    } else {
        hidden_printf("Access denied.\n");
        return 0;
    }
}

int main() {
    // Anti-debugging: check for debugger presence
    if(ptrace(0, 0, 0, 0) == -1) {
        hidden_printf("Debugger detected. Exiting.\n");
        return 1;
    }

    hidden_printf("=== Advanced Security System v2.1 ===\n");
    hidden_printf("Multi-layer authentication required.\n\n");

    // Seed random number generator for canary
    srand(time(NULL));

    if(authenticate_user()) {
        hidden_printf("\nSystem access granted!\n");
    } else {
        hidden_printf("\nAuthentication failed. System locked.\n");
    }

    return 0;
}

/*
 * ADVANCED VULNERABILITY ANALYSIS:
 *
 * This code contains multiple sophisticated vulnerabilities:
 *
 * 1. PRIMARY: gets() buffer overflow in username/password
 * 2. SECONDARY: strcpy() overflow in complex_validator()
 * 3. TERTIARY: Stack layout manipulation opportunities
 *
 * EXPLOITATION PATHS:
 *
 * Path 1 - Direct overflow:
 *   Username: "AAAAAAAAAAAAAAAA" + payload to overwrite access_level
 *
 * Path 2 - Logic bypass:
 *   Username: "overflow" (sets access_level to 50)
 *   Password: "secret" (adds 20, total = 70, but need 80+ or exactly 42)
 *
 * Path 3 - Complex overflow:
 *   Overflow local_buf in complex_validator to manipulate access_level
 *   Target value: exactly 42 or >= 80
 *
 * Path 4 - Magic number manipulation:
 *   Overflow to overwrite ctx.magic or anti_debug_var
 *
 * FLAG PROTECTION LAYERS:
 * - Multi-stage XOR encryption
 * - ROT cipher obfuscation
 * - Checksum validation
 * - Timing attack protection
 * - Anti-debugging measures
 * - Polymorphic function pointers
 *
 * The flag WOLF{buff3r_0v3rfl0w_b4s1cs} is embedded in checksum_table
 * and only revealed through complex reconstruction algorithm.
 */
