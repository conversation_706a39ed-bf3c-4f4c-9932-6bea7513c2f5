#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#define MAX_USERNAME 16
#define MAX_PASSWORD 16

int authenticate_user() {
    char username[MAX_USERNAME];
    char password[MAX_PASSWORD];
    int is_admin = 0;
    
    printf("Enter username: ");
    gets(username);  // Vulnerable function - no bounds checking!
    
    printf("Enter password: ");
    gets(password);  // Another vulnerable function!
    
    // Simple authentication check
    if (strcmp(username, "admin") == 0 && strcmp(password, "secret123") == 0) {
        is_admin = 1;
    }
    
    if (is_admin) {
        printf("Access granted! Welcome admin.\n");
        printf("FLAG: CTF{buff3r_0v3rfl0w_1s_d4ng3r0us}\n");
        return 1;
    } else {
        printf("Access denied.\n");
        return 0;
    }
}

int main() {
    printf("=== Secure Authentication System ===\n");
    printf("Please provide your credentials:\n\n");
    
    if (authenticate_user()) {
        printf("\nYou have successfully logged in!\n");
    } else {
        printf("\nAuthentication failed. Goodbye!\n");
    }
    
    return 0;
}

/*
 * VULNERABILITY ANALYSIS:
 * 
 * 1. The gets() function is used to read user input without any bounds checking
 * 2. This allows an attacker to input more than 16 characters, overflowing the buffer
 * 3. The stack layout typically looks like:
 *    [username buffer][password buffer][is_admin variable][return address]
 * 4. By overflowing the username buffer, an attacker can overwrite the is_admin variable
 * 5. Setting is_admin to any non-zero value will grant admin access
 * 
 * EXPLOITATION EXAMPLE:
 * Username: AAAAAAAAAAAAAAAA1 (16 A's + 1 to overwrite is_admin)
 * Password: anything
 * 
 * This will set is_admin = 1 (ASCII value of '1' is 49, which is non-zero)
 * 
 * SECURE ALTERNATIVES:
 * - Use fgets(username, MAX_USERNAME, stdin);
 * - Use scanf("%15s", username);
 * - Use gets_s() if available (C11)
 */
