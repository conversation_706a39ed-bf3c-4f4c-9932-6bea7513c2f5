# Buffer Overflow CTF Challenge

## Challenge Details
- **ID**: prob1
- **Title**: BUFFER OVERFLOW
- **Points**: 50
- **Difficulty**: Medium
- **Flag**: `CTF{buff3r_0v3rfl0w_1s_d4ng3r0us}`

## Description
Find the buffer overflow vulnerability in the provided C code snippet. The program is designed to authenticate users, but contains a critical security flaw that could allow an attacker to bypass authentication.

## Files Included
- `buffer_overflow.html` - Interactive web-based challenge interface
- `vulnerable_auth.c` - The vulnerable C program
- `secure_auth.c` - Fixed version showing secure implementation
- `Makefile` - Build and test utilities
- `README.md` - This file

## Quick Start

### Option 1: Web Interface
Open `buffer_overflow.html` in your web browser for an interactive experience with:
- Syntax-highlighted code
- Built-in hints
- Flag submission form
- Hidden easter egg (try the Konami code: ↑↑↓↓←→←→BA)

### Option 2: Command Line
```bash
# Compile the vulnerable version
make vulnerable

# Test it manually
make test-vulnerable

# See the exploit in action
make exploit

# Compile the secure version for comparison
make secure
make test-secure

# Get detailed explanation
make explain
```

## The Vulnerability

The vulnerable code uses the `gets()` function, which:
1. **Does not check buffer boundaries**
2. **Allows input longer than allocated buffer size**
3. **Can overwrite adjacent memory locations**

### Stack Layout
```
[username buffer][password buffer][is_admin variable][return address]
     16 bytes         16 bytes         4 bytes
```

### Exploitation
By entering more than 16 characters for the username, you can overflow the buffer and overwrite the `is_admin` variable.

**Example exploit:**
- Username: `AAAAAAAAAAAAAAAA1` (16 A's + 1)
- Password: `anything`

The character '1' (ASCII value 49) overwrites the `is_admin` variable, making it non-zero and granting admin access!

## Learning Objectives
1. Understand how buffer overflows work
2. Identify unsafe string functions (`gets`, `strcpy`, `sprintf`)
3. Learn secure alternatives (`fgets`, `strncpy`, `snprintf`)
4. Recognize the importance of input validation

## Security Fixes
The secure version (`secure_auth.c`) demonstrates proper fixes:
- Replace `gets()` with `fgets()` with size limits
- Add proper input validation
- Handle newline characters correctly

## Additional Security Measures
For production code, consider:
- Stack canaries (`-fstack-protector`)
- Address Space Layout Randomization (ASLR)
- Data Execution Prevention (DEP/NX bit)
- Secure password hashing (bcrypt, Argon2)
- Rate limiting for failed attempts
- Input sanitization and validation

## Compilation Notes
- Vulnerable version disables stack protection for educational purposes
- Secure version enables modern security features
- Use appropriate compiler flags in production

## Hint
Look for unsafe string functions that don't check buffer boundaries. The `gets()` function is notorious for buffer overflow vulnerabilities!

---

**Warning**: This code is intentionally vulnerable for educational purposes. Never use `gets()` in production code!
