#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#define MAX_USERNAME 16
#define MAX_PASSWORD 16

int authenticate_user() {
    char username[MAX_USERNAME];
    char password[MAX_PASSWORD];
    int is_admin = 0;
    
    printf("Enter username: ");
    // FIXED: Use fgets() with size limit instead of gets()
    if (fgets(username, MAX_USERNAME, stdin) != NULL) {
        // Remove newline character if present
        size_t len = strlen(username);
        if (len > 0 && username[len-1] == '\n') {
            username[len-1] = '\0';
        }
    }
    
    printf("Enter password: ");
    // FIXED: Use fgets() with size limit instead of gets()
    if (fgets(password, MAX_PASSWORD, stdin) != NULL) {
        // Remove newline character if present
        size_t len = strlen(password);
        if (len > 0 && password[len-1] == '\n') {
            password[len-1] = '\0';
        }
    }
    
    // Simple authentication check
    if (strcmp(username, "admin") == 0 && strcmp(password, "secret123") == 0) {
        is_admin = 1;
    }
    
    if (is_admin) {
        printf("Access granted! Welcome admin.\n");
        printf("FLAG: CTF{buff3r_0v3rfl0w_1s_d4ng3r0us}\n");
        return 1;
    } else {
        printf("Access denied.\n");
        return 0;
    }
}

int main() {
    printf("=== Secure Authentication System ===\n");
    printf("Please provide your credentials:\n\n");
    
    if (authenticate_user()) {
        printf("\nYou have successfully logged in!\n");
    } else {
        printf("\nAuthentication failed. Goodbye!\n");
    }
    
    return 0;
}

/*
 * SECURITY IMPROVEMENTS:
 * 
 * 1. Replaced gets() with fgets() which respects buffer boundaries
 * 2. Added proper newline handling to clean up input
 * 3. Added null pointer checks for fgets() return value
 * 4. Buffer overflow is now prevented
 * 
 * ADDITIONAL SECURITY MEASURES (for production):
 * - Use secure password hashing (bcrypt, scrypt, Argon2)
 * - Implement rate limiting for failed attempts
 * - Add input validation and sanitization
 * - Use constant-time comparison for passwords
 * - Add logging for security events
 */
